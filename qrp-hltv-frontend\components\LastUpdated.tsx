interface LastUpdatedProps {
  timestamp?: string;
  className?: string;
}

export default function LastUpdated({ timestamp, className = "" }: LastUpdatedProps) {
  if (!timestamp) return null;

  const formatTime = (isoString: string) => {
    try {
      const date = new Date(isoString);
      return date.toLocaleString();
    } catch {
      return 'Unknown';
    }
  };

  return (
    <div className={`text-xs text-zinc-500 ${className}`}>
      Last updated: {formatTime(timestamp)}
    </div>
  );
}
