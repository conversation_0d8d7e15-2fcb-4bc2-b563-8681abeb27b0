const fs = require('fs')
const path = require('path')

// Check backup status and health
function checkBackupStatus() {
  console.log('🔍 Backup System Status Check')
  console.log('─'.repeat(50))
  
  const backupDir = path.join(process.cwd(), 'backups')
  const dailyDir = path.join(backupDir, 'daily')
  const weeklyDir = path.join(backupDir, 'weekly')
  
  // Check if backup directories exist
  if (!fs.existsSync(backupDir)) {
    console.log('❌ No backup directory found')
    console.log('💡 Run "npm run backup" to create your first backup')
    return
  }
  
  // Check daily backups
  console.log('📅 Daily Backups:')
  if (fs.existsSync(dailyDir)) {
    const dailyBackups = fs.readdirSync(dailyDir)
      .filter(dir => fs.statSync(path.join(dailyDir, dir)).isDirectory())
      .sort()
      .reverse()
    
    if (dailyBackups.length === 0) {
      console.log('   ⚠️  No daily backups found')
    } else {
      console.log(`   ✅ ${dailyBackups.length} daily backups available`)
      console.log(`   📅 Latest: ${dailyBackups[0]}`)
      console.log(`   📅 Oldest: ${dailyBackups[dailyBackups.length - 1]}`)
      
      // Check latest backup details
      const latestBackupPath = path.join(dailyDir, dailyBackups[0])
      const manifestPath = path.join(latestBackupPath, 'manifest.json')
      
      if (fs.existsSync(manifestPath)) {
        try {
          const manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf8'))
          console.log(`   📦 Documents in latest: ${manifest.summary.totalDocuments}`)
          console.log(`   🕐 Created: ${new Date(manifest.timestamp).toLocaleString()}`)
        } catch (error) {
          console.log('   ⚠️  Could not read latest backup manifest')
        }
      }
    }
  } else {
    console.log('   ❌ No daily backup directory')
  }
  
  console.log('')
  
  // Check weekly backups
  console.log('📅 Weekly Backups:')
  if (fs.existsSync(weeklyDir)) {
    const weeklyBackups = fs.readdirSync(weeklyDir)
      .filter(dir => fs.statSync(path.join(weeklyDir, dir)).isDirectory())
      .sort()
      .reverse()
    
    if (weeklyBackups.length === 0) {
      console.log('   ⚠️  No weekly backups found')
    } else {
      console.log(`   ✅ ${weeklyBackups.length} weekly backups available`)
      console.log(`   📅 Latest: ${weeklyBackups[0]}`)
      console.log(`   📅 Oldest: ${weeklyBackups[weeklyBackups.length - 1]}`)
    }
  } else {
    console.log('   ❌ No weekly backup directory')
  }
  
  console.log('')
  
  // Check backup freshness
  console.log('🕐 Backup Freshness:')
  if (fs.existsSync(dailyDir)) {
    const dailyBackups = fs.readdirSync(dailyDir)
      .filter(dir => fs.statSync(path.join(dailyDir, dir)).isDirectory())
      .sort()
      .reverse()
    
    if (dailyBackups.length > 0) {
      const latestDate = new Date(dailyBackups[0])
      const today = new Date()
      const daysDiff = Math.floor((today - latestDate) / (1000 * 60 * 60 * 24))
      
      if (daysDiff === 0) {
        console.log('   ✅ Latest backup is from today')
      } else if (daysDiff === 1) {
        console.log('   ✅ Latest backup is from yesterday')
      } else if (daysDiff <= 3) {
        console.log(`   ⚠️  Latest backup is ${daysDiff} days old`)
      } else {
        console.log(`   ❌ Latest backup is ${daysDiff} days old - consider running a backup`)
      }
    }
  }
  
  console.log('')
  
  // Storage usage
  console.log('💾 Storage Usage:')
  if (fs.existsSync(backupDir)) {
    try {
      const { execSync } = require('child_process')
      const size = execSync(`du -sh "${backupDir}" 2>/dev/null || echo "Unknown"`, { encoding: 'utf8' }).trim()
      console.log(`   📁 Total backup size: ${size.split('\t')[0] || 'Unknown'}`)
    } catch (error) {
      console.log('   📁 Could not determine backup size')
    }
  }
  
  console.log('')
  
  // Recommendations
  console.log('💡 Recommendations:')
  
  if (!fs.existsSync(dailyDir) || fs.readdirSync(dailyDir).length === 0) {
    console.log('   🔧 Run "npm run backup" to create your first backup')
  }
  
  if (fs.existsSync(dailyDir)) {
    const dailyBackups = fs.readdirSync(dailyDir).filter(dir => 
      fs.statSync(path.join(dailyDir, dir)).isDirectory()
    )
    
    if (dailyBackups.length > 0) {
      const latestDate = new Date(dailyBackups.sort().reverse()[0])
      const daysDiff = Math.floor((new Date() - latestDate) / (1000 * 60 * 60 * 24))
      
      if (daysDiff > 1) {
        console.log('   🔧 Consider running a fresh backup')
      }
    }
  }
  
  console.log('   📖 Check BACKUP_README.md for detailed instructions')
  console.log('   🔄 Automated backups run daily via GitHub Actions')
  
  console.log('')
  console.log('🎉 Backup system is configured and ready!')
}

// Run status check if called directly
if (require.main === module) {
  checkBackupStatus()
}

module.exports = { checkBackupStatus }
