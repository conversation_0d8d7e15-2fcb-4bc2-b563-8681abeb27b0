// Debug script to check partners data from Sanity
// Run with: node debug-partners.js
// Make sure you have the environment variables set

const { createClient } = require('@sanity/client');

// Load environment variables if .env file exists
try {
  require('dotenv').config();
} catch (e) {
  // dotenv not available, that's ok
}

const debugPartners = async () => {
  console.log('🔍 Debugging partners data...\n');

  // Check environment variables
  const projectId = process.env.NEXT_PUBLIC_SANITY_PROJECT_ID || process.env.SANITY_PROJECT_ID;
  const dataset = process.env.NEXT_PUBLIC_SANITY_DATASET || process.env.SANITY_DATASET;
  const token = process.env.SANITY_TOKEN;

  console.log('Environment check:');
  console.log(`  - Project ID: ${projectId ? projectId.substring(0, 8) + '...' : 'NOT SET'}`);
  console.log(`  - Dataset: ${dataset || 'NOT SET'}`);
  console.log(`  - Token: ${token ? 'SET' : 'NOT SET'}\n`);

  if (!projectId || !dataset) {
    console.error('❌ Missing required environment variables');
    console.log('Please set NEXT_PUBLIC_SANITY_PROJECT_ID and NEXT_PUBLIC_SANITY_DATASET');
    return;
  }

  // Create Sanity client
  const client = createClient({
    projectId,
    dataset,
    apiVersion: '2023-05-24',
    useCdn: false,
    token,
    perspective: 'published'
  });

  try {
    console.log('📡 Fetching partners from Sanity...');
    
    // Fetch partners with the same query as the frontend
    const partners = await client.fetch(`*[_type == "partner" && !(_id in path("drafts.**"))] | order(order asc, name asc){
      _id,
      name,
      logo,
      description,
      website,
      order,
      _updatedAt,
      _createdAt
    }`);

    console.log(`✅ Found ${partners.length} partners\n`);

    if (partners.length > 0) {
      console.log('📋 Partners data:');
      partners.forEach((partner, index) => {
        console.log(`\n${index + 1}. ${partner.name}`);
        console.log(`   ID: ${partner._id}`);
        console.log(`   Order: ${partner.order}`);
        console.log(`   Website: ${partner.website || 'Not set'}`);
        console.log(`   Created: ${partner._createdAt}`);
        console.log(`   Updated: ${partner._updatedAt}`);
        console.log(`   Description: ${partner.description?.substring(0, 50)}${partner.description?.length > 50 ? '...' : ''}`);
      });
    } else {
      console.log('⚠️ No partners found');
    }

    // Also check for draft partners
    console.log('\n🔍 Checking for draft partners...');
    const drafts = await client.fetch(`*[_type == "partner" && (_id in path("drafts.**"))]{
      _id,
      name,
      _updatedAt
    }`);

    if (drafts.length > 0) {
      console.log(`📝 Found ${drafts.length} draft partners:`);
      drafts.forEach(draft => {
        console.log(`   - ${draft.name} (${draft._id})`);
      });
    } else {
      console.log('✅ No draft partners found');
    }

  } catch (error) {
    console.error('❌ Error fetching partners:', error.message);
    console.log('\n🔍 Possible causes:');
    console.log('   - Invalid Sanity credentials');
    console.log('   - Network connectivity issues');
    console.log('   - Sanity project/dataset configuration issues');
  }
};

// Only run if this file is executed directly
if (require.main === module) {
  debugPartners();
}

module.exports = { debugPartners };
