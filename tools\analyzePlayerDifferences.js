const { createClient } = require('@sanity/client')
const fs = require('fs')
const { parse } = require('csv-parse')
require('dotenv').config()

// Initialize Sanity client
const client = createClient({
  projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID || process.env.SANITY_PROJECT_ID,
  dataset: process.env.NEXT_PUBLIC_SANITY_DATASET || process.env.SANITY_DATASET,
  token: process.env.SANITY_TOKEN,
  apiVersion: '2024-03-24',
  useCdn: false,
})

// Configuration
const CSV_FILE_PATH = 'players list.csv'
const NICKNAME_COLUMN_INDEX = 2
const ELO_DIFFERENCE_THRESHOLD = 2 // ELO differences below this are considered rounding errors

async function fetchSanityPlayers() {
  const players = await client.fetch(`
    *[_type == "player" && !(_id in path("drafts.**"))] {
      _id,
      nickname,
      name,
      elo,
      _createdAt,
      _updatedAt
    }
  `)
  return players
}

async function readCSVPlayers(filePath) {
  return new Promise((resolve, reject) => {
    const players = []
    const parser = parse({
      skip_empty_lines: true,
      from_line: 7,
    })

    parser.on('readable', function() {
      let record
      while (record = parser.read()) {
        const nickname = record[NICKNAME_COLUMN_INDEX]
        const elo = parseFloat(record[4]?.replace(',', '.')) || 0

        // Skip players with ELO 0 and ensure nickname is valid
        if (nickname && nickname.trim() && nickname !== 'Nickname' && elo > 0) {
          players.push({
            nickname: nickname.trim(),
            name: record[3] || '',
            elo: elo,
            position: parseInt(record[1]) || 0
          })
        }
      }
    })

    parser.on('error', reject)
    parser.on('end', () => resolve(players))

    const fileContent = fs.readFileSync(filePath, 'utf8')
    parser.write(fileContent)
    parser.end()
  })
}

function findSimilarNames(targetName, namesList, threshold = 0.8) {
  const similar = []
  const target = targetName.toLowerCase()
  
  for (const name of namesList) {
    const candidate = name.toLowerCase()
    
    // Exact match
    if (target === candidate) continue
    
    // Check if one contains the other
    if (target.includes(candidate) || candidate.includes(target)) {
      similar.push({ name, similarity: 'contains' })
      continue
    }
    
    // Simple character similarity
    const similarity = calculateSimilarity(target, candidate)
    if (similarity >= threshold) {
      similar.push({ name, similarity: similarity.toFixed(2) })
    }
  }
  
  return similar
}

function calculateSimilarity(str1, str2) {
  const longer = str1.length > str2.length ? str1 : str2
  const shorter = str1.length > str2.length ? str2 : str1
  
  if (longer.length === 0) return 1.0
  
  const editDistance = levenshteinDistance(longer, shorter)
  return (longer.length - editDistance) / longer.length
}

function levenshteinDistance(str1, str2) {
  const matrix = []
  
  for (let i = 0; i <= str2.length; i++) {
    matrix[i] = [i]
  }
  
  for (let j = 0; j <= str1.length; j++) {
    matrix[0][j] = j
  }
  
  for (let i = 1; i <= str2.length; i++) {
    for (let j = 1; j <= str1.length; j++) {
      if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
        matrix[i][j] = matrix[i - 1][j - 1]
      } else {
        matrix[i][j] = Math.min(
          matrix[i - 1][j - 1] + 1,
          matrix[i][j - 1] + 1,
          matrix[i - 1][j] + 1
        )
      }
    }
  }
  
  return matrix[str2.length][str1.length]
}

function analyzeELODifferences(sanityPlayers, csvPlayers, threshold = 5) {
  const differences = []
  const roundingErrors = []

  for (const sanityPlayer of sanityPlayers) {
    const csvPlayer = csvPlayers.find(p =>
      p.nickname.toLowerCase().trim() === sanityPlayer.nickname.toLowerCase().trim()
    )

    if (csvPlayer) {
      const sanityElo = sanityPlayer.elo || 0
      const csvElo = csvPlayer.elo || 0
      const difference = Math.abs(sanityElo - csvElo)

      if (difference > 0) {
        const diffData = {
          nickname: sanityPlayer.nickname,
          sanityElo,
          csvElo,
          difference,
          percentageDiff: sanityElo > 0 ? ((difference / sanityElo) * 100).toFixed(1) : 'N/A'
        }

        // Separate significant differences from rounding errors
        if (difference >= threshold) {
          differences.push(diffData)
        } else {
          roundingErrors.push(diffData)
        }
      }
    }
  }

  return {
    significantDifferences: differences.sort((a, b) => b.difference - a.difference),
    roundingErrors: roundingErrors.sort((a, b) => b.difference - a.difference)
  }
}

async function generateDetailedAnalysis() {
  console.log('🔍 Generating detailed player analysis...\n')
  
  const [sanityPlayers, csvPlayers] = await Promise.all([
    fetchSanityPlayers(),
    readCSVPlayers(CSV_FILE_PATH)
  ])
  
  // Create nickname sets for comparison
  const sanityNicknames = new Set(sanityPlayers.map(p => p.nickname.toLowerCase().trim()))
  const csvNicknames = new Set(csvPlayers.map(p => p.nickname.toLowerCase().trim()))
  
  // Find players only in Sanity
  const onlyInSanity = sanityPlayers.filter(player => 
    !csvNicknames.has(player.nickname.toLowerCase().trim())
  )
  
  // Find players only in CSV
  const onlyInCSV = csvPlayers.filter(player => 
    !sanityNicknames.has(player.nickname.toLowerCase().trim())
  )
  
  // Analyze ELO differences for common players
  const eloAnalysis = analyzeELODifferences(sanityPlayers, csvPlayers, ELO_DIFFERENCE_THRESHOLD)
  
  console.log('📊 DETAILED ANALYSIS RESULTS')
  console.log('='.repeat(80))
  
  // Potential matches analysis
  console.log('\n🔍 POTENTIAL SIMILAR NAMES (might be the same players):')
  console.log('-'.repeat(60))
  
  let potentialMatches = 0
  for (const sanityPlayer of onlyInSanity.slice(0, 10)) { // Limit to first 10 for readability
    const csvNicknamesList = onlyInCSV.map(p => p.nickname)
    const similar = findSimilarNames(sanityPlayer.nickname, csvNicknamesList, 0.7)
    
    if (similar.length > 0) {
      console.log(`\n  Sanity: "${sanityPlayer.nickname}" might match:`)
      similar.slice(0, 3).forEach(match => {
        console.log(`    → CSV: "${match.name}" (similarity: ${match.similarity})`)
      })
      potentialMatches++
    }
  }
  
  if (potentialMatches === 0) {
    console.log('  No obvious similar names found in the first 10 entries.')
  }
  
  // ELO differences analysis
  if (eloAnalysis.significantDifferences.length > 0) {
    console.log(`\n📈 SIGNIFICANT ELO DIFFERENCES (≥${ELO_DIFFERENCE_THRESHOLD} points):`)
    console.log('-'.repeat(60))
    eloAnalysis.significantDifferences.slice(0, 10).forEach((diff, index) => {
      console.log(`${(index + 1).toString().padStart(3)}. ${diff.nickname}`)
      console.log(`     Sanity: ${diff.sanityElo} | CSV: ${diff.csvElo} | Diff: ${diff.difference} (${diff.percentageDiff}%)`)
    })
  } else {
    console.log(`\n✅ NO SIGNIFICANT ELO DIFFERENCES FOUND (≥${ELO_DIFFERENCE_THRESHOLD} points)`)
    console.log('   All ELO differences are minor rounding variations.')
  }

  // Show summary of rounding errors
  if (eloAnalysis.roundingErrors.length > 0) {
    console.log(`\n📝 MINOR ROUNDING DIFFERENCES (<${ELO_DIFFERENCE_THRESHOLD} points): ${eloAnalysis.roundingErrors.length} players`)
    console.log('   These are likely due to decimal precision and can be ignored.')
  }
  
  // Note about filtered players
  console.log(`\n📝 NOTE: Players with ELO 0 have been filtered out from CSV comparison`)
  console.log('   This focuses the analysis on active players only.')
  
  // High ELO players only in CSV
  const highEloOnlyInCSV = onlyInCSV.filter(p => p.elo > 1000)
  if (highEloOnlyInCSV.length > 0) {
    console.log(`\n🏆 HIGH ELO PLAYERS ONLY IN CSV (ELO > 1000): ${highEloOnlyInCSV.length}`)
    console.log('-'.repeat(60))
    highEloOnlyInCSV.slice(0, 5).forEach(player => {
      console.log(`  • ${player.nickname} - ELO: ${player.elo} - Position: ${player.position}`)
    })
  }
  
  // Summary statistics
  const avgSanityElo = sanityPlayers.reduce((sum, p) => sum + (p.elo || 0), 0) / sanityPlayers.length
  const avgCSVElo = csvPlayers.reduce((sum, p) => sum + p.elo, 0) / csvPlayers.length

  console.log('\n📊 STATISTICS:')
  console.log('-'.repeat(60))
  console.log(`  Average ELO in Sanity: ${avgSanityElo.toFixed(1)}`)
  console.log(`  Average ELO in CSV (active players): ${avgCSVElo.toFixed(1)}`)
  console.log(`  Players with significant ELO differences (≥${ELO_DIFFERENCE_THRESHOLD}): ${eloAnalysis.significantDifferences.length}`)
  console.log(`  Players with minor rounding differences (<${ELO_DIFFERENCE_THRESHOLD}): ${eloAnalysis.roundingErrors.length}`)
  
  // Save detailed report
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
  const reportPath = `tools/detailed-analysis-${timestamp}.json`
  
  const report = {
    timestamp: new Date().toISOString(),
    summary: {
      totalSanity: sanityPlayers.length,
      totalCSV: csvPlayers.length,
      onlyInSanity: onlyInSanity.length,
      onlyInCSV: onlyInCSV.length,
      avgSanityElo: parseFloat(avgSanityElo.toFixed(1)),
      avgCSVElo: parseFloat(avgCSVElo.toFixed(1)),
      significantEloDifferences: eloAnalysis.significantDifferences.length,
      minorRoundingDifferences: eloAnalysis.roundingErrors.length,
      note: "Players with ELO 0 filtered out from CSV; ELO differences <5 considered rounding errors"
    },
    significantEloDifferences: eloAnalysis.significantDifferences.slice(0, 20), // Top 20 significant differences
    roundingErrors: eloAnalysis.roundingErrors.slice(0, 10), // Top 10 rounding errors for reference
    highEloOnlyInCSV,
    onlyInSanity: onlyInSanity.slice(0, 20),
    onlyInCSV: onlyInCSV.slice(0, 50)
  }
  
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2))
  console.log(`\n📄 Detailed analysis saved to: ${reportPath}`)
  
  console.log('\n' + '='.repeat(80))
}

// Run the analysis
if (require.main === module) {
  generateDetailedAnalysis().catch(console.error)
}

module.exports = { generateDetailedAnalysis }
