# Maintenance Tools

This directory contains utility scripts for maintaining the CARX HLTV application. These tools are not part of the production application and are meant to be run locally for maintenance tasks.

## Available Tools

### Player Comparison Tool (`comparePlayersWithCSV.js`)

Compares players between your Sanity CMS database and a CSV file to identify discrepancies.

**Usage:**
```bash
npm run compare-players
```

**What it does:**
- Fetches all players from Sanity CMS
- Reads player data from `players list.csv` (filters out ELO 0 players)
- Compares the two datasets and shows differences
- Generates a detailed JSON report

### Player Analysis Tool (`analyzePlayerDifferences.js`)

Provides detailed analysis of player data differences, including ELO comparisons and potential name matches.

**Usage:**
```bash
npm run analyze-players
```

**Features:**
- ELO difference analysis for common players (filters out rounding errors <5 points)
- Identifies potential similar names (duplicate detection)
- Statistics about average ELO ratings
- Focuses on active players only (ELO > 0)
- Separates significant differences from minor rounding variations

### Player Import Tool (`importPlayers.js`)

This script allows you to import or update player data from a CSV file into the Sanity CMS.

#### Prerequisites

1. A valid Sanity token in your `.env` file:
   ```
   NEXT_PUBLIC_SANITY_PROJECT_ID=your_project_id
   NEXT_PUBLIC_SANITY_DATASET=your_dataset
   SANITY_TOKEN=your_token
   ```

2. A CSV file named `players list.csv` in the root directory with the following columns:
   - Nickname (required)
   - Name
   - Elo

#### Usage

Run the script using npm:
```bash
npm run import-players
```

The script will:
1. Validate your environment variables
2. Test the connection to Sanity
3. Read and parse the CSV file
4. Create or update players in Sanity
5. Log detailed information about the process

#### Error Handling

The script includes comprehensive error logging to help diagnose any issues that might occur during the import process. Check the console output for detailed error messages if something goes wrong.

## CSV File Format

The comparison and analysis scripts expect a CSV file named `players list.csv` in the root directory with the following structure:

- **Column A (index 0)**: Empty/metadata
- **Column B (index 1)**: Position/ranking
- **Column C (index 2)**: **Nickname** (main identifier for comparison)
- **Column D (index 3)**: Real name
- **Column E (index 4)**: ELO rating
- **Rows 1-6**: Headers and metadata (skipped)
- **Row 7+**: Player data

## Configuration

You can modify the following constants in the scripts if needed:

```javascript
const CSV_FILE_PATH = 'players list.csv' // Path to your CSV file
const NICKNAME_COLUMN_INDEX = 2 // Column containing nicknames (0-indexed)
const ELO_DIFFERENCE_THRESHOLD = 5 // ELO differences below this are considered rounding errors
```

## Understanding Comparison Results

### Common Scenarios

1. **Players only in Sanity**:
   - Manually added players
   - Players with nickname variations
   - Test/duplicate entries

2. **Players only in CSV**:
   - New active players not yet imported to Sanity
   - Players with special characters in names
   - Note: ELO 0 players are automatically filtered out

3. **ELO Differences**:
   - Small differences due to decimal precision
   - Recent updates in one system but not the other

## Recommended Workflow

1. **Compare first**: `npm run compare-players`
2. **Analyze details**: `npm run analyze-players`
3. **Review results**: Check console output and JSON reports
4. **Import if needed**: `npm run import-players`
5. **Cleanup duplicates**: `npm run cleanup-players`

## Generated Reports

All scripts generate timestamped JSON reports in the `tools/` directory for detailed analysis and record-keeping.