// Verify that the newly created support request has proper rich text format
const { createClient } = require('@sanity/client')

const client = createClient({
  projectId: 'al3wd5y8',
  dataset: 'production',
  useCdn: false,
  apiVersion: '2023-05-03',
  token: process.env.SANITY_API_TOKEN,
})

async function verifyNewSupportRequest() {
  try {
    console.log('🔍 Fetching the most recent support request...')
    
    // Fetch the most recent support request
    const recentRequest = await client.fetch(`
      *[_type == "supportRequest" && !(_id in path("drafts.**"))] | order(_createdAt desc)[0] {
        _id,
        _createdAt,
        subject,
        message,
        name,
        discord
      }
    `)
    
    if (!recentRequest) {
      console.log('❌ No support requests found')
      return
    }
    
    console.log(`📋 Most recent support request:`)
    console.log(`- ID: ${recentRequest._id}`)
    console.log(`- Subject: ${recentRequest.subject}`)
    console.log(`- From: ${recentRequest.name} (${recentRequest.discord})`)
    console.log(`- Created: ${recentRequest._createdAt}`)
    console.log(`- Message type: ${typeof recentRequest.message}`)
    console.log(`- Message is array: ${Array.isArray(recentRequest.message)}`)
    
    if (Array.isArray(recentRequest.message)) {
      console.log('✅ Message is properly formatted as rich text (array)')
      console.log(`- Number of blocks: ${recentRequest.message.length}`)
      if (recentRequest.message.length > 0) {
        console.log(`- First block type: ${recentRequest.message[0]._type}`)
        if (recentRequest.message[0].children && recentRequest.message[0].children.length > 0) {
          console.log(`- Text content: "${recentRequest.message[0].children[0].text}"`)
        }
      }
    } else {
      console.log('❌ Message is still in string format - API update may not be working')
      console.log(`- Message content: "${recentRequest.message}"`)
    }
    
  } catch (error) {
    console.error('❌ Verification failed:', error.message)
  }
}

// Run the verification
verifyNewSupportRequest()
