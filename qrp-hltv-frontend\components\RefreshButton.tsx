import { useState } from 'react';

interface RefreshButtonProps {
  className?: string;
}

export default function RefreshButton({ className = "" }: RefreshButtonProps) {
  const [isRefreshing, setIsRefreshing] = useState(false);

  const handleRefresh = async () => {
    setIsRefreshing(true);
    
    try {
      // Clear browser cache
      if ('caches' in window) {
        const cacheNames = await caches.keys();
        await Promise.all(
          cacheNames.map(name => caches.delete(name))
        );
      }
      
      // Force reload with cache bypass
      window.location.reload();
    } catch (error) {
      console.error('Error refreshing:', error);
      // Fallback to simple reload
      window.location.reload();
    }
  };

  return (
    <button
      onClick={handleRefresh}
      disabled={isRefreshing}
      className={`
        px-3 py-1 text-xs bg-yellow-600 hover:bg-yellow-500 
        disabled:bg-yellow-800 disabled:cursor-not-allowed
        text-white rounded transition-colors
        ${className}
      `}
      title="Refresh page to get latest content"
    >
      {isRefreshing ? '🔄' : '↻'} Refresh
    </button>
  );
}
