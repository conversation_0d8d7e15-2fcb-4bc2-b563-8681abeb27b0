// Utility functions for admin styling and role management

export const isAdmin = (role?: string) => ['creator', 'support'].includes(role || '')

export const getAdminNameClasses = (role?: string, baseClasses?: string) => {
  if (!isAdmin(role)) {
    return baseClasses || 'font-bold text-lg transition'
  }

  // Orange gradient glow for creator, green for support
  if (role === 'creator') {
    return 'font-bold transition bg-gradient-to-r from-orange-400 via-orange-500 to-orange-600 bg-clip-text text-transparent drop-shadow-[0_0_8px_rgba(251,146,60,0.8)]'
  } else if (role === 'support') {
    return 'font-bold transition bg-gradient-to-r from-green-400 via-green-500 to-green-600 bg-clip-text text-transparent drop-shadow-[0_0_8px_rgba(34,197,94,0.8)]'
  }

  return baseClasses || 'font-bold text-lg transition'
}
