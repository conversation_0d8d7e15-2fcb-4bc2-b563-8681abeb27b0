// Utility functions for admin styling and role management

export const isAdmin = (role?: string) => ['creator', 'support'].includes(role || '')

export const getAdminNameClasses = (role?: string, baseClasses?: string) => {
  if (!isAdmin(role)) {
    return baseClasses || 'font-bold text-lg transition'
  }

  // Orange gradient glow for creator, green for support
  if (role === 'creator') {
    return 'font-bold transition bg-gradient-to-r from-orange-400 via-orange-300 to-orange-400 bg-clip-text text-transparent drop-shadow-[0_0_12px_rgba(251,146,60,1)]'
  } else if (role === 'support') {
    return 'font-bold transition bg-gradient-to-r from-green-400 via-green-300 to-green-400 bg-clip-text text-transparent drop-shadow-[0_0_12px_rgba(34,197,94,1)]'
  }

  return baseClasses || 'font-bold text-lg transition'
}
