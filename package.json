{"name": "qrp-hltv", "private": true, "version": "1.0.0", "main": "package.json", "license": "UNLICENSED", "scripts": {"dev": "sanity dev", "start": "sanity start", "build": "npm run build-studio && npm run build-frontend", "build-studio": "sanity build -y --output-path ./qrp-hltv-frontend/public/studio", "build-frontend": "cd qrp-hltv-frontend && npm install && npm run build", "deploy": "sanity deploy", "deploy-graphql": "sanity graphql deploy", "import-players": "node tools/importPlayers.js", "cleanup-players": "node tools/cleanupPlayers.js", "compare-players": "node tools/comparePlayersWithCSV.js", "analyze-players": "node tools/analyzePlayerDifferences.js", "migrate-partners": "node tools/migratePartnerDescriptions.js", "migrate-news": "node tools/migrateNewsText.js", "migrate-support": "node tools/migrateSupportRequestText.js", "migrate-all-text": "npm run migrate-partners && npm run migrate-news && npm run migrate-support", "backup": "node tools/backup.js", "backup-full": "node tools/backup.js --full", "backup-status": "node tools/backup-status.js", "restore": "node tools/restore.js"}, "keywords": ["sanity"], "dependencies": {"@portabletext/react": "^3.2.1", "@sanity/client": "^7.3.0", "@sanity/vision": "^3.89.0", "csv-parse": "^5.6.0", "dotenv": "^16.5.0", "lucide-react": "^0.511.0", "react": "^18.2.0", "react-dom": "^18.2.0", "sanity": "^3.89.0", "sonner": "^2.0.3", "styled-components": "^6.1.15"}, "devDependencies": {"@sanity/eslint-config-studio": "^5.0.2", "@types/node": "^22.15.21", "@types/react": "^18.0.25", "eslint": "^9.9.0", "prettier": "^3.0.2", "ts-node": "^10.9.2", "typescript": "^5.8.3"}, "prettier": {"semi": false, "printWidth": 100, "bracketSpacing": false, "singleQuote": true}}