name: Automated CMS Backup

permissions:
  contents: write

on:
  # Run daily at 2 AM UTC
  schedule:
    - cron: '0 2 * * *'
  
  # Allow manual trigger
  workflow_dispatch:
    inputs:
      backup_type:
        description: 'Backup type'
        required: true
        default: 'published'
        type: choice
        options:
          - published
          - full

jobs:
  backup:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4
      with:
        token: ${{ secrets.GITHUB_TOKEN }}
        fetch-depth: 0
    
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm install
    
    - name: Configure Git
      run: |
        git config --local user.email "<EMAIL>"
        git config --local user.name "GitHub Action"
    
    - name: Run backup (Published only)
      if: github.event.inputs.backup_type != 'full'
      env:
        SANITY_PROJECT_ID: ${{ secrets.SANITY_PROJECT_ID }}
        SANITY_DATASET: ${{ secrets.SANITY_DATASET }}
        SANITY_TOKEN: ${{ secrets.SANITY_TOKEN }}
      run: npm run backup
    
    - name: Run backup (Full with drafts)
      if: github.event.inputs.backup_type == 'full'
      env:
        SANITY_PROJECT_ID: ${{ secrets.SANITY_PROJECT_ID }}
        SANITY_DATASET: ${{ secrets.SANITY_DATASET }}
        SANITY_TOKEN: ${{ secrets.SANITY_TOKEN }}
      run: npm run backup-full
    
    - name: Check if backup was created
      id: check_backup
      run: |
        if [ -d "backups" ] && [ "$(ls -A backups/daily)" ]; then
          echo "backup_exists=true" >> $GITHUB_OUTPUT
          echo "backup_date=$(date +%Y-%m-%d)" >> $GITHUB_OUTPUT
        else
          echo "backup_exists=false" >> $GITHUB_OUTPUT
        fi
    
    - name: Commit and push backup
      if: steps.check_backup.outputs.backup_exists == 'true'
      run: |
        git add backups/
        if git diff --staged --quiet; then
          echo "No changes to commit"
        else
          git commit -m "Automated backup: ${{ steps.check_backup.outputs.backup_date }}"
          git push
        fi
    
    - name: Clean old backups (keep last 30 days)
      run: |
        if [ -d "backups/daily" ]; then
          find backups/daily -type d -name "20*" -mtime +30 -exec rm -rf {} + 2>/dev/null || true
        fi
    
    - name: Commit cleanup
      run: |
        if [ -d "backups" ]; then
          git add backups/
          if ! git diff --staged --quiet; then
            git commit -m "Cleanup: Remove backups older than 30 days"
            git push
          fi
        fi
    
    - name: Create backup summary
      if: steps.check_backup.outputs.backup_exists == 'true'
      run: |
        echo "## 📦 Backup Summary" >> $GITHUB_STEP_SUMMARY
        echo "**Date:** ${{ steps.check_backup.outputs.backup_date }}" >> $GITHUB_STEP_SUMMARY
        echo "**Type:** ${{ github.event.inputs.backup_type || 'published' }}" >> $GITHUB_STEP_SUMMARY
        
        if [ -f "backups/daily/${{ steps.check_backup.outputs.backup_date }}/manifest.json" ]; then
          echo "**Details:**" >> $GITHUB_STEP_SUMMARY
          node -e "
            const manifest = require('./backups/daily/${{ steps.check_backup.outputs.backup_date }}/manifest.json');
            console.log('- Total documents: ' + manifest.summary.totalDocuments);
            console.log('- Successful types: ' + manifest.summary.successfulTypes);
            console.log('- Failed types: ' + manifest.summary.failedTypes);
            manifest.results.forEach(r => {
              if (r.status === 'success') {
                console.log('- ' + r.contentType + ': ' + r.count + ' documents');
              }
            });
          " >> $GITHUB_STEP_SUMMARY
        fi
    
    - name: Notify on failure
      if: failure()
      run: |
        echo "## ❌ Backup Failed" >> $GITHUB_STEP_SUMMARY
        echo "The automated backup process failed. Please check the logs and run a manual backup." >> $GITHUB_STEP_SUMMARY
