import { sanity, urlFor } from '../lib/sanity'
import Image from 'next/image'
import Head from 'next/head'
import { useLanguage } from '../lib/LanguageContext'
import { useState, useEffect, useMemo } from 'react'
import { useRouter } from 'next/router'
import 'flag-icons/css/flag-icons.min.css'
import { getAdminNameClasses } from '../lib/adminUtils'

interface Player {
  _id: string;
  name: string;
  nickname: string;
  elo: number;
  image: any;
  country: string;
  city?: string;
  age?: number;
  discord?: string;
  role?: string;
}

interface Team {
  _id: string;
  name: string;
  logo: any;
  players: Player[];
  teamElo: number; // Pre-calculated team ELO
}

// Helper function to calculate team ELO as average of players with valid ELO > 0
const calculateTeamElo = (players: { elo: number }[]): number => {
  if (!players || players.length === 0) {
    return 0; // Return 0 for teams with no players
  }

  // Filter players with valid ELO > 0 (exclude 0, null, undefined, NaN)
  const validPlayers = players.filter(player =>
    player.elo && !isNaN(player.elo) && player.elo > 0
  );

  if (validPlayers.length === 0) {
    return 0; // Return 0 if no players have valid ELO data > 0
  }

  const totalElo = validPlayers.reduce((sum, player) => sum + player.elo, 0);
  return Math.round(totalElo / validPlayers.length);
};

export async function getStaticProps() {
  const teams = await sanity.fetch(`*[_type == "team" && !(_id in path("drafts.**"))]{
    _id,
    name,
    logo,
    "players": *[_type == "player" && references(^._id)]{
      _id,
      name,
      nickname,
      elo,
      image,
      country,
      city,
      age,
      discord,
      role
    }
  }`)
  // Calculate team ELO for each team and add it to the team object
  const teamsWithElo = teams.map((team: any) => ({
    ...team,
    teamElo: calculateTeamElo(team.players)
  }));

  // Sort teams by ELO in descending order (highest ELO first)
  const sortedTeams = teamsWithElo.sort((a: any, b: any) => b.teamElo - a.teamElo);

  return {
    props: { teams: sortedTeams },
    revalidate: 60, // Revalidate every 60 seconds
  }
}

export default function TeamsPage({ teams }: { teams: Team[] }) {
  const { t } = useLanguage();
  const router = useRouter();
  const [selectedTeam, setSelectedTeam] = useState<Team | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [toastMessage, setToastMessage] = useState<string | null>(null);

  const handleCopyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text).then(() => {
      setToastMessage(t("toast_copy_success"));
    }).catch(err => {
      console.error('Failed to copy: ', err);
      setToastMessage(t("toast_copy_failed"));
    });
  };

  // Handle query parameter to auto-open team modal
  useEffect(() => {
    if (router.isReady && router.query.open && teams.length > 0) {
      const teamId = router.query.open as string;
      const team = teams.find(t => t._id === teamId);
      if (team) {
        setSelectedTeam(team);
        // Clean up URL without triggering navigation
        router.replace('/teams', undefined, { shallow: true });
      }
    }
  }, [router.isReady, router.query.open, teams]);

  useEffect(() => {
    if (toastMessage) {
      const timer = setTimeout(() => {
        setToastMessage(null);
      }, 2000);
      return () => clearTimeout(timer);
    }
  }, [toastMessage]);

  // Function to navigate to players page with auto-open player modal
  const navigateToPlayer = (playerId: string) => {
    router.push(`/players?open=${playerId}`);
  };

  // Filter teams based on search term while maintaining ELO sorting
  const filteredTeams = useMemo(() => {
    if (!searchTerm) {
      return teams;
    }
    return teams.filter(team =>
      team.name.toLowerCase().includes(searchTerm.toLowerCase())
    );
  }, [teams, searchTerm]);

  return (
    <>
      <Head>
        <title>{`${t('teams_page_title')} - QRP HLTV`}</title>
        <meta name="description" content="CarX Drift Racing teams rankings, rosters and statistics" />
        <meta property="og:title" content={`${t('teams_page_title')} - QRP HLTV`} />
        <meta property="og:description" content="CarX Drift Racing teams rankings, rosters and statistics" />
        <meta property="og:url" content="https://qrp-hltv.com/teams" />
        <meta name="twitter:title" content={`${t('teams_page_title')} - QRP HLTV`} />
        <meta name="twitter:description" content="CarX Drift Racing teams rankings, rosters and statistics" />
      </Head>
      <main className="max-w-6xl mx-auto py-12 px-4">
        <div className="flex items-center mb-8">
          <h1 className="text-3xl font-bold mr-4">{t('teams_page_title')}</h1>
          <div className="flex-1 h-1 bg-orange-700 rounded-full opacity-40" />
        </div>
        <div className="mb-8">
          <input
            type="text"
            placeholder={t('search_team_placeholder') || "Search by team name..."}
            className="w-full px-4 py-2 rounded-lg bg-zinc-800 border border-zinc-700 focus:ring-2 focus:ring-orange-600 focus:border-orange-600 outline-none transition"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {filteredTeams.map((team) => {
            const rank = teams.findIndex(t => t._id === team._id) + 1;
            return (
              <div
                key={team._id}
                className="bg-zinc-900 rounded-xl border border-zinc-800 overflow-hidden hover:border-orange-600 transition group flex items-start p-4 cursor-pointer"
              onClick={() => setSelectedTeam(team)}
            >
              <div className="flex-shrink-0 w-28 h-28 relative overflow-hidden bg-zinc-800 p-2 mr-4 rounded-md">
                {team.logo ? (
                  <Image
                    src={urlFor(team.logo).width(96).height(96).url()}
                    alt={team.name}
                    width={96}
                    height={96}
                    className="object-contain"
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center text-zinc-600">
                    {t('no_logo_label')}
                  </div>
                )}
              </div>
              <div className="flex-grow">
                <div className="mb-2">
                  <h3 className="font-bold text-xl group-hover:text-orange-400 transition">{team.name}</h3>
                  {/* Rank: {rank} */}
                </div>
                <div className="mt-2 flex items-center">
                  {team.teamElo > 0 ? (
                    <>
                      <span className="text-orange-400 font-semibold">{t('elo_label')} {team.teamElo}</span>
                      <span className="ml-3 text-lg">
                        {rank === 1 && '🥇'}
                        {rank === 2 && '🥈'}
                        {rank === 3 && '🥉'}
                        {rank > 3 && `#${rank}`}
                      </span>
                    </>
                  ) : (
                    <span className="text-zinc-500 text-sm">{t('no_elo_data_label') || 'No ELO data available'}</span>
                  )}
                </div>
              </div>
            </div>
            );
          })}
        </div>

        {/* Team Modal */}
        {selectedTeam && (
          <div
            className="fixed inset-0 bg-black/70 flex items-center justify-center z-50 p-4 backdrop-blur-sm"
            onClick={() => setSelectedTeam(null)}
          >
            <div
              className="bg-zinc-900 rounded-2xl p-6 max-w-4xl w-full max-h-[90vh] overflow-y-auto border border-zinc-700 shadow-2xl"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="flex justify-between items-start mb-6">
                <div className="flex items-center gap-4">
                  {selectedTeam.logo && (
                    <div className="w-16 h-16 relative overflow-hidden bg-zinc-800 p-2 rounded-md">
                      <Image
                        src={urlFor(selectedTeam.logo).width(64).height(64).url()}
                        alt={selectedTeam.name}
                        width={64}
                        height={64}
                        className="object-contain"
                      />
                    </div>
                  )}
                  <div>
                    <h2 className="text-2xl font-bold text-orange-400">{selectedTeam.name}</h2>
                    <p className="text-zinc-400">
                      {selectedTeam.teamElo > 0 ? (
                        <span>{t('elo_label')} {selectedTeam.teamElo}</span>
                      ) : (
                        <span>{t('no_elo_data_label') || 'No ELO data available'}</span>
                      )}
                    </p>
                  </div>
                </div>
                <button
                  onClick={() => setSelectedTeam(null)}
                  className="text-zinc-400 hover:text-zinc-100 transition p-1 rounded-md hover:bg-zinc-700"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>

              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-orange-300 mb-4">
                  {t('team_players_label') || 'Team Players'} ({selectedTeam.players.length})
                </h3>

                {selectedTeam.players.length > 0 ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {selectedTeam.players
                      .sort((a, b) => b.elo - a.elo) // Sort players by ELO descending
                      .map((player) => (
                        <div
                          key={player._id}
                          className="bg-zinc-800 rounded-lg p-4 border border-zinc-700 hover:border-orange-500 transition cursor-pointer group"
                          onClick={() => navigateToPlayer(player._id)}
                          title={`View ${player.nickname} profile`}
                        >
                          <div className="flex items-start gap-3">
                            <div className="w-16 h-16 relative overflow-hidden bg-zinc-700 rounded-lg flex-shrink-0">
                              {player.image ? (
                                <Image
                                  src={urlFor(player.image).width(64).height(64).url()}
                                  alt={player.name}
                                  width={64}
                                  height={64}
                                  className="object-cover"
                                />
                              ) : (
                                <div className="w-full h-full flex items-center justify-center text-zinc-500 text-xs">
                                  {t('no_image_label')}
                                </div>
                              )}
                            </div>
                            <div className="flex-grow min-w-0">
                              <div className="flex items-center gap-2 mb-1">
                                <h4 className={getAdminNameClasses(player.role, "font-bold text-orange-300 group-hover:text-orange-200 transition")}>{player.nickname}</h4>
                                {player.country && (
                                  <span className={`fi fi-${player.country.toLowerCase()} text-sm`}
                                        title={player.country} />
                                )}
                              </div>
                              {player.name && (
                                <p className="text-zinc-400 text-sm mb-1">{player.name}</p>
                              )}
                              <p className="text-orange-400 font-semibold text-sm">{t('elo_label')} {player.elo}</p>

                              <div className="mt-2 space-y-1 text-xs text-zinc-400">
                                {player.country && (
                                  <p><span className="font-medium">{t('country_label')}:</span> {player.country}</p>
                                )}
                                {player.city && (
                                  <p><span className="font-medium">{t('city_label')}:</span> {player.city}</p>
                                )}
                                {player.age && (
                                  <p><span className="font-medium">{t('age_label')}:</span> {player.age}</p>
                                )}
                                {player.discord && (
                                  <div className="flex items-center">
                                    <button
                                      onClick={() => handleCopyToClipboard(player.discord!)}
                                      className="text-zinc-400 hover:text-orange-400 transition flex items-center text-xs"
                                      title={t('copy_discord_tip')}
                                    >
                                      <svg className="h-3 w-3 mr-1" role="img" viewBox="0 -28.5 256 256" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlnsXlink="http://www.w3.org/1999/xlink" preserveAspectRatio="xMidYMid" fill="currentColor">
                                        <g><path d="M216.856339,16.5966031 C200.285002,8.84328665 182.566144,3.2084988 164.041564,0 C161.766523,4.11318106 159.108624,9.64549908 157.276099,14.0464379 C137.583995,11.0849896 118.072967,11.0849896 98.7430163,14.0464379 C96.9108417,9.64549908 94.1925838,4.11318106 91.8971895,0 C73.3526068,3.2084988 55.6133949,8.86399117 39.0420583,16.6376612 C5.61752293,67.146514 -3.4433191,116.400813 1.08711069,164.955721 C23.2560196,181.510915 44.7403634,191.567697 65.8621325,198.148576 C71.0772151,190.971126 75.7283628,183.341335 79.7352139,175.300261 C72.104019,172.400575 64.7949724,168.822202 57.8887866,164.667963 C59.7209612,163.310589 61.5131304,161.891452 63.2445898,160.431257 C105.36741,180.133187 151.134928,180.133187 192.754523,160.431257 C194.506336,161.891452 196.298154,163.310589 198.110326,164.667963 C191.183787,168.842556 183.854737,172.420929 176.223542,175.320965 C180.230393,183.341335 184.861538,190.991831 190.096624,198.16893 C211.238746,191.588051 232.743023,181.531619 254.911949,164.955721 C260.227747,108.668201 245.831087,59.8662432 216.856339,16.5966031 Z M85.4738752,135.09489 C72.8290281,135.09489 62.4592217,123.290155 62.4592217,108.914901 C62.4592217,94.5396472 72.607595,82.7145587 85.4738752,82.7145587 C98.3405064,82.7145587 108.709962,94.5189427 108.488529,108.914901 C108.508531,123.290155 98.3405064,135.09489 85.4738752,135.09489 Z M170.525237,135.09489 C157.88039,135.09489 147.510584,123.290155 147.510584,108.914901 C147.510584,94.5396472 157.658606,82.7145587 170.525237,82.7145587 C183.391518,82.7145587 193.761324,94.5189427 193.539891,108.914901 C193.539891,123.290155 183.391518,135.09489 170.525237,135.09489 Z"></path></g>
                                      </svg>
                                      <span>{player.discord}</span>
                                    </button>
                                  </div>
                                )}
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                  </div>
                ) : (
                  <p className="text-zinc-500 text-center py-8">{t('no_players_label') || 'No players in this team'}</p>
                )}
              </div>
            </div>
          </div>
        )}
      </main>

      {toastMessage && (
        <div
          className="fixed top-5 right-5 bg-zinc-700 text-zinc-100 py-2 px-4 rounded-md shadow-lg transition-opacity duration-300 z-[9999]"
          aria-live="assertive"
          role="status"
        >
          {toastMessage}
        </div>
      )}
    </>
  )
}