import { useState, useEffect } from 'react'
import Head from 'next/head'
import Image from 'next/image'
import { useLanguage } from '../../lib/LanguageContext'

export default function AdminPage() {
  const { t } = useLanguage()
  const [terminalStep, setTerminalStep] = useState(0)
  const [showContent, setShowContent] = useState(false)

  useEffect(() => {
    const timer1 = setTimeout(() => setTerminalStep(1), 800)
    const timer2 = setTimeout(() => setTerminalStep(2), 1600)
    const timer3 = setTimeout(() => setTerminalStep(3), 2400)
    const timer4 = setTimeout(() => setShowContent(true), 3200)

    return () => {
      clearTimeout(timer1)
      clearTimeout(timer2)
      clearTimeout(timer3)
      clearTimeout(timer4)
    }
  }, [])

  return (
    <>
      <Head>
        <title>{`${t('admin_page_title')} - QRP HLTV`}</title>
        <meta name="description" content="Admin tribute page - QRP HLTV developer information" />
        <meta name="robots" content="noindex, nofollow" />
      </Head>

      <main className="max-w-4xl mx-auto py-12 px-4">
        {/* Terminal Animation */}
        <div className="mb-12">
          <div className="bg-zinc-950 border border-zinc-700 rounded-lg p-6 font-mono text-sm shadow-2xl">
            <div className="flex items-center mb-4">
              <div className="flex space-x-2">
                <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                <div className="w-3 h-3 bg-green-500 rounded-full"></div>
              </div>
              <div className="ml-4 text-zinc-400 text-xs">admin@qrp-hltv:~$</div>
            </div>
            
            <div className="space-y-2">
              {terminalStep >= 1 && (
                <div className="flex items-center">
                  <span className="text-green-400">&gt;</span>
                  <span className="ml-2 text-zinc-300">{t('admin_accessing')}</span>
                  {terminalStep === 1 && <span className="ml-1 animate-pulse">|</span>}
                </div>
              )}
              
              {terminalStep >= 2 && (
                <div className="flex items-center">
                  <span className="text-green-400">&gt;</span>
                  <span className="ml-2 text-green-300">{t('admin_permission_granted')}</span>
                  {terminalStep === 2 && <span className="ml-1 animate-pulse">|</span>}
                </div>
              )}
              
              {terminalStep >= 3 && (
                <div className="flex items-center">
                  <span className="text-green-400">&gt;</span>
                  <span className="ml-2 text-blue-300">{t('admin_welcome')}</span>
                  {terminalStep === 3 && <span className="ml-1 animate-pulse">|</span>}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Content */}
        {showContent && (
          <div className="space-y-8 animate-fade-in">
            {/* Prank Reveal */}
            <div className="bg-gradient-to-r from-orange-900/20 to-red-900/20 border border-orange-500/30 rounded-2xl p-8 shadow-lg">
              <div className="flex items-center gap-4 mb-4">
                <h2 className="text-2xl font-bold text-transparent bg-gradient-to-r from-orange-400 to-red-400 bg-clip-text">
                  {t('admin_prank_title')}
                </h2>
                {/* Custom Laughing Sticker */}
                <div className="relative w-12 h-12 animate-bounce">
                  <div className="w-12 h-12 bg-gradient-to-br from-yellow-400 to-orange-400 rounded-full shadow-lg flex items-center justify-center">
                    {/* Eyes */}
                    <div className="absolute top-3 left-2 w-1.5 h-1.5 bg-zinc-800 rounded-full"></div>
                    <div className="absolute top-3 right-2 w-1.5 h-1.5 bg-zinc-800 rounded-full"></div>
                    {/* Mouth */}
                    <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 w-6 h-3 border-2 border-zinc-800 border-t-0 rounded-b-full"></div>
                  </div>
                  {/* Sparkle effect */}
                  <div className="absolute -top-1 -right-1 w-3 h-3 text-yellow-300 animate-pulse">✨</div>
                </div>
              </div>
              <p className="text-zinc-300 leading-relaxed">
                {t('admin_prank_message')}
              </p>
            </div>

            {/* Developer Credit */}
            <div className="bg-zinc-900 border border-zinc-800 rounded-2xl p-8 shadow-lg">
              <h2 className="text-2xl font-bold mb-6 text-transparent bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text">
                {t('admin_built_by')}
              </h2>
              <div className="flex items-center space-x-6">
                {/* Avatar */}
                <div className="flex-shrink-0">
                  <div className="relative group">
                    <Image
                      src="/envy_avatar.webp"
                      alt="Envy Avatar"
                      width={120}
                      height={120}
                      className="rounded-full border-4 border-blue-500/30 shadow-lg transition-all duration-300 group-hover:border-blue-400/50 group-hover:shadow-blue-500/20 group-hover:shadow-xl"
                      priority
                    />
                    <div className="absolute inset-0 rounded-full bg-gradient-to-tr from-blue-500/20 to-purple-500/20 transition-opacity duration-300 group-hover:from-blue-500/30 group-hover:to-purple-500/30"></div>
                    <div className="absolute -inset-1 rounded-full bg-gradient-to-tr from-blue-500/10 to-purple-500/10 blur-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  </div>
                </div>

                {/* Developer Info */}
                <div className="flex-1">
                  <div className="text-2xl font-bold text-zinc-100 mb-2">
                    <span className="text-transparent bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text">Envy</span>
                  </div>
                  <div className="text-zinc-300 text-lg mb-3">
                    {t('admin_developer_title')}
                  </div>
                  <div className="flex flex-wrap gap-2">
                    <span className="px-3 py-1 bg-blue-500/20 text-blue-300 rounded-full text-sm border border-blue-500/30 hover:bg-blue-500/30 hover:border-blue-400/50 transition-all duration-200 cursor-default">
                      {t('admin_role_fullstack')}
                    </span>
                    <span className="px-3 py-1 bg-purple-500/20 text-purple-300 rounded-full text-sm border border-purple-500/30 hover:bg-purple-500/30 hover:border-purple-400/50 transition-all duration-200 cursor-default">
                      {t('admin_role_designer')}
                    </span>
                    <span className="px-3 py-1 bg-green-500/20 text-green-300 rounded-full text-sm border border-green-500/30 hover:bg-green-500/30 hover:border-green-400/50 transition-all duration-200 cursor-default">
                      {t('admin_role_enthusiast')}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {/* Tech Stack */}
            <div className="bg-zinc-900 border border-zinc-800 rounded-2xl p-8 shadow-lg">
              <h2 className="text-2xl font-bold mb-6 text-transparent bg-gradient-to-r from-green-400 to-cyan-400 bg-clip-text">
                {t('admin_tech_stack')}
              </h2>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="bg-zinc-800 border border-zinc-700 rounded-lg p-4 text-center hover:border-blue-500 transition-colors">
                  <div className="text-blue-400 font-semibold">Next.js</div>
                  <div className="text-xs text-zinc-400 mt-1">React Framework</div>
                </div>
                <div className="bg-zinc-800 border border-zinc-700 rounded-lg p-4 text-center hover:border-red-500 transition-colors">
                  <div className="text-red-400 font-semibold">Sanity CMS</div>
                  <div className="text-xs text-zinc-400 mt-1">Content Management</div>
                </div>
                <div className="bg-zinc-800 border border-zinc-700 rounded-lg p-4 text-center hover:border-cyan-500 transition-colors">
                  <div className="text-cyan-400 font-semibold">Tailwind CSS</div>
                  <div className="text-xs text-zinc-400 mt-1">Styling</div>
                </div>
                <div className="bg-zinc-800 border border-zinc-700 rounded-lg p-4 text-center hover:border-green-500 transition-colors">
                  <div className="text-green-400 font-semibold">Netlify</div>
                  <div className="text-xs text-zinc-400 mt-1">Hosting & Deploy</div>
                </div>
              </div>
            </div>

            {/* Contact */}
            <div className="bg-zinc-900 border border-zinc-800 rounded-2xl p-8 shadow-lg">
              <h2 className="text-2xl font-bold mb-6 text-transparent bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text">
                {t('admin_contact')}
              </h2>
              <div className="flex flex-wrap items-center gap-4">
                <div className="flex items-center space-x-2 bg-zinc-800 border border-zinc-700 rounded-lg px-4 py-3 hover:border-purple-500 transition-colors">
                  <svg className="w-5 h-5 text-purple-400" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M20.317 4.37a19.791 19.791 0 0 0-4.885-1.515.074.074 0 0 0-.079.037c-.21.375-.444.864-.608 1.25a18.27 18.27 0 0 0-5.487 0 12.64 12.64 0 0 0-.617-************ 0 0 0-.079-.037A19.736 19.736 0 0 0 3.677 4.37a.07.07 0 0 0-.032.027C.533 9.046-.32 13.58.099 18.057a.082.082 0 0 0 .031.057 19.9 19.9 0 0 0 5.993 ************ 0 0 0 .084-.028 14.09 14.09 0 0 0 1.226-1.994.076.076 0 0 0-.041-.106 13.107 13.107 0 0 1-1.872-.892.077.077 0 0 1-.008-.128 10.2 10.2 0 0 0 .372-.292.074.074 0 0 1 .077-.01c3.928 1.793 8.18 1.793 12.062 0a.074.074 0 0 1 .078.01c.**************.373.292a.077.077 0 0 1-.006.127 12.299 12.299 0 0 1-1.873.892.077.077 0 0 0-.041.107c.36.698.772 1.362 1.225 1.993a.076.076 0 0 0 .084.028 19.839 19.839 0 0 0 6.002-3.03.077.077 0 0 0 .032-.054c.5-5.177-.838-9.674-3.549-13.66a.061.061 0 0 0-.031-.03zM8.02 15.33c-1.183 0-2.157-1.085-2.157-2.419 0-1.333.956-2.419 2.157-2.419 1.21 0 2.176 1.096 2.157 2.42 0 1.333-.956 2.418-2.157 2.418zm7.975 0c-1.183 0-2.157-1.085-2.157-2.419 0-1.333.955-2.419 2.157-2.419 1.21 0 2.176 1.096 2.157 2.42 0 1.333-.946 2.418-2.157 2.418z"/>
                  </svg>
                  <span className="text-zinc-300 font-mono text-sm">@lagcomp</span>
                </div>
                <a
                  href="https://envycarx.netlify.app/"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center space-x-2 bg-zinc-800 border border-zinc-700 rounded-lg px-4 py-3 hover:border-cyan-500 transition-colors group"
                >
                  <svg className="w-5 h-5 text-cyan-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                  </svg>
                  <span className="text-zinc-300 text-sm group-hover:text-cyan-300 transition-colors">Carx Hub</span>
                </a>
              </div>
              <p className="text-zinc-400 text-sm mt-4">
                {t('admin_contact_description')}
              </p>
            </div>


          </div>
        )}
      </main>

      <style jsx>{`
        @keyframes fade-in {
          from {
            opacity: 0;
            transform: translateY(20px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
        
        .animate-fade-in {
          animation: fade-in 0.8s ease-out;
        }
      `}</style>
    </>
  )
}
