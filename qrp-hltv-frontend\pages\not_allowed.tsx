import Head from 'next/head'

export default function NotAllowedPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-zinc-950 via-zinc-900 to-zinc-800 text-zinc-100 flex items-center justify-center">
      <Head>
        <title>Доступ ограничен</title>
        <meta name="description" content="Доступ к сайту временно ограничен" />
        <meta name="robots" content="noindex, nofollow" />
      </Head>
      
      <div className="text-center px-4">
        <div className="max-w-md mx-auto">
          <h1 className="text-4xl font-bold text-red-400 mb-8">
            Доступ ограничен
          </h1>
          
          <div className="bg-zinc-800/50 backdrop-blur-sm rounded-lg p-8 border border-zinc-700">
            <p className="text-xl text-zinc-200 leading-relaxed">
              Даже для тебя, инки, доступ запрещен.
            </p>
          </div>
          
          <div className="mt-8 text-sm text-zinc-400">
            <p>Сайт временно недоступен, обратитесь в QRP для объяснения.</p>
          </div>
        </div>
      </div>
    </div>
  )
}
