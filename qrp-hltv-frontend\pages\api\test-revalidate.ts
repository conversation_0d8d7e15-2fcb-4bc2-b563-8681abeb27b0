import type { NextApiRequest, NextApiResponse } from 'next'

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  console.log('='.repeat(50));
  console.log('TEST REVALIDATE ENDPOINT CALLED');
  console.log('Method:', req.method);
  console.log('Query:', JSON.stringify(req.query));
  console.log('Body:', JSON.stringify(req.body, null, 2));

  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' });
  }

  // Check for secret to confirm this is a valid request
  if (req.query.secret !== process.env.SANITY_WEBHOOK_SECRET) {
    console.error('❌ Invalid webhook secret provided.');
    return res.status(401).json({
      message: 'Invalid token',
      receivedSecret: req.query.secret ? 'provided' : 'missing',
      expectedSecret: process.env.SANITY_WEBHOOK_SECRET ? 'configured' : 'missing'
    });
  }

  try {
    // Get the document type from query params or body
    const documentType = req.query.type || req.body?._type || 'partner';
    const testPaths: string[] = [];

    console.log(`Testing revalidation for document type: ${documentType}`);

    // Determine paths based on document type
    switch (documentType) {
      case 'player':
        testPaths.push('/players/', '/teams/', '/');
        break;
      case 'team':
        testPaths.push('/teams/', '/players/', '/');
        break;
      case 'news':
        testPaths.push('/news/', '/');
        break;
      case 'tournament':
        testPaths.push('/tournaments/', '/');
        break;
      case 'partner':
        testPaths.push('/partners/', '/');
        break;
      default:
        testPaths.push('/');
    }

    console.log(`Paths to revalidate: ${testPaths.join(', ')}`);

    const revalidationResults = [];

    for (const path of testPaths) {
      try {
        console.log(`Attempting to revalidate: ${path}`);
        
        if (typeof res.revalidate === 'function') {
          await res.revalidate(path);
          console.log(`✅ Successfully revalidated: ${path}`);
          revalidationResults.push({ path, status: 'success' });
        } else {
          console.warn(`⚠️ res.revalidate is not available. Type: ${typeof res.revalidate}`);
          revalidationResults.push({
            path,
            status: 'error',
            reason: 'res.revalidate function not available'
          });
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        console.error(`❌ Failed to revalidate ${path}:`, errorMessage);
        revalidationResults.push({ path, status: 'error', reason: errorMessage });
      }
    }

    return res.json({
      success: true,
      message: `Test revalidation completed for ${documentType}`,
      documentType,
      pathsTested: testPaths,
      results: revalidationResults,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Test revalidation error:', error);
    return res.status(500).json({
      success: false,
      message: 'Test revalidation failed',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}
