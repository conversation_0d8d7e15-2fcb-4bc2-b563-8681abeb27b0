/**
 * Player Import Tool for CARX HLTV
 * 
 * This script is a maintenance tool for importing/updating player data from a CSV file into Sanity CMS.
 * It should be run locally when you need to update player data.
 * 
 * Prerequisites:
 * 1. Make sure you have a valid SANITY_TOKEN in your .env file
 * 2. Ensure your CSV file is named 'players list.csv' and contains the columns: Nickname, Name, Elo
 * 
 * Usage:
 * npm run import-players
 * 
 * Note: This script is not part of the production application and should only be used for maintenance.
 */

const { createClient } = require('@sanity/client')
const fs = require('fs')
const { parse } = require('csv-parse')
const dotenv = require('dotenv')

// Load environment variables
dotenv.config()

// Debug: Log environment variables (without exposing the full token)
console.log('Environment variables loaded:')
console.log('Project ID:', process.env.NEXT_PUBLIC_SANITY_PROJECT_ID)
console.log('Dataset:', process.env.NEXT_PUBLIC_SANITY_DATASET)

// Validate required environment variables
if (!process.env.NEXT_PUBLIC_SANITY_PROJECT_ID) {
  throw new Error('Missing NEXT_PUBLIC_SANITY_PROJECT_ID in environment variables')
}
if (!process.env.NEXT_PUBLIC_SANITY_DATASET) {
  throw new Error('Missing NEXT_PUBLIC_SANITY_DATASET in environment variables')
}
if (!process.env.SANITY_TOKEN) {
  throw new Error('Missing SANITY_TOKEN in environment variables')
}

// Initialize Sanity client
const client = createClient({
  projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID,
  dataset: process.env.NEXT_PUBLIC_SANITY_DATASET,
  token: process.env.SANITY_TOKEN,
  apiVersion: '2024-03-24',
  useCdn: false,
})

// Test connection and permissions
async function testConnection() {
  try {
    console.log('Testing Sanity connection...')
    const result = await client.fetch('*[_type == "player"][0]')
    console.log('Connection test result:', result ? 'Success' : 'No documents found')
    return true
  } catch (error) {
    console.error('Connection test failed:', error.message)
    return false
  }
}

async function importPlayers() {
  // Test connection first
  const connectionValid = await testConnection()
  if (!connectionValid) {
    console.error('Failed to establish connection to Sanity. Please check your credentials.')
    process.exit(1)
  }

  const players = []

  console.log('Reading CSV file...')
  // Read the CSV file
  const fileContent = fs.readFileSync('players list.csv', 'utf-8')

  // Parse CSV without headers since the structure is complex
  await new Promise((resolve, reject) => {
    parse(fileContent, {
      columns: false, // Don't use first row as headers
      skip_empty_lines: true,
      trim: true,
      from_line: 7, // Skip the first 6 header rows
    }, (err, records) => {
      if (err) {
        console.error('Error parsing CSV:', err)
        reject(err)
        return
      }

      console.log(`Found ${records.length} data rows`)

      // Process each record
      records.forEach((record, index) => {
        // Column mapping: [0]=empty, [1]=rank, [2]=nickname, [3]=name, [4]=elo, [5]=score, ...
        const nickname = record[2] ? record[2].trim() : ''
        const name = record[3] ? record[3].trim() : ''
        const eloStr = record[4] ? record[4].trim() : ''

        if (nickname && nickname.toLowerCase() !== 'nickname' && nickname !== '') {
          // Convert European decimal format (comma) to US format (dot)
          const eloValue = eloStr.replace(',', '.')
          const elo = parseFloat(eloValue)

          // Skip players with ELO 0 or invalid ELO
          if (isNaN(elo) || elo === 0) {
            console.log(`Row ${index + 7}: Skipping player with ELO 0 or invalid ELO: "${nickname}" (ELO: ${eloStr})`)
            return
          }

          const roundedElo = Math.round(elo)

          const player = {
            nickname: nickname,
            name: name,
            elo: roundedElo,
          }
          players.push(player)
          console.log(`Row ${index + 7}: Found player:`, player)
        } else if (nickname) {
          console.log(`Row ${index + 7}: Skipping invalid row with nickname: "${nickname}"`)
        }
      })
      resolve(records)
    })
  })

  // Import to Sanity with batch processing
  console.log(`\nImporting ${players.length} players to Sanity...`)

  // First, fetch all existing players in one query
  console.log('Fetching existing players...')
  const existingPlayers = await client.fetch(
    `*[_type == "player"]{_id, nickname, elo}`
  )

  // Create a map for quick lookup
  const existingPlayersMap = new Map()
  existingPlayers.forEach(player => {
    existingPlayersMap.set(player.nickname, player)
  })

  console.log(`Found ${existingPlayers.length} existing players`)

  // Separate players into updates and creates
  const playersToUpdate = []
  const playersToCreate = []

  players.forEach(player => {
    const existing = existingPlayersMap.get(player.nickname)
    if (existing) {
      // Only update if ELO has changed
      if (existing.elo !== player.elo) {
        playersToUpdate.push({
          id: existing._id,
          nickname: player.nickname,
          elo: player.elo
        })
      }
    } else {
      playersToCreate.push(player)
    }
  })

  console.log(`Players to update: ${playersToUpdate.length}`)
  console.log(`Players to create: ${playersToCreate.length}`)

  // Process updates in batches
  const BATCH_SIZE = 10
  let updateCount = 0

  if (playersToUpdate.length > 0) {
    console.log('\nProcessing updates...')
    for (let i = 0; i < playersToUpdate.length; i += BATCH_SIZE) {
      const batch = playersToUpdate.slice(i, i + BATCH_SIZE)

      try {
        // Process batch in parallel
        await Promise.all(batch.map(async (player) => {
          await client
            .patch(player.id)
            .set({
              nickname: player.nickname,
              elo: player.elo,
            })
            .commit()
          updateCount++
          if (updateCount % 50 === 0) {
            console.log(`Updated ${updateCount}/${playersToUpdate.length} players...`)
          }
        }))
      } catch (error) {
        console.error(`Error updating batch starting at index ${i}:`, error.message)
      }
    }
    console.log(`Successfully updated ${updateCount} players`)
  }

  // Process creates in batches
  let createCount = 0

  if (playersToCreate.length > 0) {
    console.log('\nProcessing new players...')
    for (let i = 0; i < playersToCreate.length; i += BATCH_SIZE) {
      const batch = playersToCreate.slice(i, i + BATCH_SIZE)

      try {
        // Process batch in parallel
        await Promise.all(batch.map(async (player) => {
          await client.create({
            _type: 'player',
            ...player,
          })
          createCount++
          if (createCount % 50 === 0) {
            console.log(`Created ${createCount}/${playersToCreate.length} players...`)
          }
        }))
      } catch (error) {
        console.error(`Error creating batch starting at index ${i}:`, error.message)
      }
    }
    console.log(`Successfully created ${createCount} players`)
  }

  console.log('\nImport completed!')
}

// Run the import
importPlayers().catch(error => {
  console.error('Fatal error:', error)
  process.exit(1)
}) 