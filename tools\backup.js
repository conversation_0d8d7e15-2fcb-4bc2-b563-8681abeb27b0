const { createClient } = require('@sanity/client')
const fs = require('fs')
const path = require('path')
require('dotenv').config()

// Sanity client configuration
const client = createClient({
  projectId: process.env.SANITY_PROJECT_ID,
  dataset: process.env.SANITY_DATASET,
  apiVersion: '2023-05-24',
  token: process.env.SANITY_TOKEN,
  useCdn: false
})

// Content types to backup
const CONTENT_TYPES = [
  'player',
  'team', 
  'news',
  'tournament',
  'partner',
  'supportRequest'
]

// Create backup directory structure
function createBackupDirectories() {
  const backupDir = path.join(process.cwd(), 'backups')
  const timestamp = new Date().toISOString().split('T')[0] // YYYY-MM-DD format
  const dailyDir = path.join(backupDir, 'daily', timestamp)
  
  if (!fs.existsSync(backupDir)) {
    fs.mkdirSync(backupDir, { recursive: true })
  }
  
  if (!fs.existsSync(dailyDir)) {
    fs.mkdirSync(dailyDir, { recursive: true })
  }
  
  return { backupDir, dailyDir, timestamp }
}

// Export documents for a specific content type
async function exportContentType(contentType, outputDir) {
  try {
    console.log(`📦 Backing up ${contentType}...`)
    
    // Fetch all documents of this type (including drafts for full backup)
    const isFullBackup = process.argv.includes('--full')
    const query = isFullBackup 
      ? `*[_type == "${contentType}"]`
      : `*[_type == "${contentType}" && !(_id in path("drafts.**"))]`
    
    const documents = await client.fetch(query)
    
    if (documents.length === 0) {
      console.log(`   ⚠️  No ${contentType} documents found`)
      return { contentType, count: 0, status: 'empty' }
    }
    
    // Save to JSON file
    const filename = `${contentType}.json`
    const filepath = path.join(outputDir, filename)
    
    fs.writeFileSync(filepath, JSON.stringify(documents, null, 2))
    
    console.log(`   ✅ Backed up ${documents.length} ${contentType} documents`)
    return { contentType, count: documents.length, status: 'success', filepath }
    
  } catch (error) {
    console.error(`   ❌ Failed to backup ${contentType}:`, error.message)
    return { contentType, count: 0, status: 'error', error: error.message }
  }
}

// Export all assets (images, files)
async function exportAssets(outputDir) {
  try {
    console.log(`🖼️  Backing up assets...`)
    
    // Get all asset references
    const assets = await client.fetch(`*[_type in ["sanity.imageAsset", "sanity.fileAsset"]]`)
    
    if (assets.length === 0) {
      console.log(`   ⚠️  No assets found`)
      return { contentType: 'assets', count: 0, status: 'empty' }
    }
    
    // Save asset metadata
    const filename = 'assets.json'
    const filepath = path.join(outputDir, filename)
    
    fs.writeFileSync(filepath, JSON.stringify(assets, null, 2))
    
    console.log(`   ✅ Backed up ${assets.length} asset references`)
    return { contentType: 'assets', count: assets.length, status: 'success', filepath }
    
  } catch (error) {
    console.error(`   ❌ Failed to backup assets:`, error.message)
    return { contentType: 'assets', count: 0, status: 'error', error: error.message }
  }
}

// Create backup manifest
function createManifest(results, outputDir, timestamp) {
  const manifest = {
    timestamp: new Date().toISOString(),
    date: timestamp,
    projectId: process.env.SANITY_PROJECT_ID,
    dataset: process.env.SANITY_DATASET,
    backupType: process.argv.includes('--full') ? 'full' : 'published',
    results: results,
    summary: {
      totalDocuments: results.reduce((sum, r) => sum + r.count, 0),
      successfulTypes: results.filter(r => r.status === 'success').length,
      failedTypes: results.filter(r => r.status === 'error').length,
      emptyTypes: results.filter(r => r.status === 'empty').length
    }
  }
  
  const manifestPath = path.join(outputDir, 'manifest.json')
  fs.writeFileSync(manifestPath, JSON.stringify(manifest, null, 2))
  
  return manifest
}

// Main backup function
async function runBackup() {
  console.log('🚀 Starting Sanity CMS backup...')
  console.log(`📅 Date: ${new Date().toISOString()}`)
  console.log(`🏗️  Project: ${process.env.SANITY_PROJECT_ID}`)
  console.log(`📊 Dataset: ${process.env.SANITY_DATASET}`)
  console.log(`🔧 Backup type: ${process.argv.includes('--full') ? 'Full (including drafts)' : 'Published only'}`)
  console.log('─'.repeat(50))
  
  try {
    // Test connection
    await client.fetch('*[_type == "player"][0]')
    console.log('✅ Sanity connection verified')
  } catch (error) {
    console.error('❌ Failed to connect to Sanity:', error.message)
    process.exit(1)
  }
  
  // Create backup directories
  const { dailyDir, timestamp } = createBackupDirectories()
  console.log(`📁 Backup directory: ${dailyDir}`)
  console.log('─'.repeat(50))
  
  // Backup all content types
  const results = []
  
  for (const contentType of CONTENT_TYPES) {
    const result = await exportContentType(contentType, dailyDir)
    results.push(result)
  }
  
  // Backup assets
  const assetResult = await exportAssets(dailyDir)
  results.push(assetResult)
  
  console.log('─'.repeat(50))
  
  // Create manifest
  const manifest = createManifest(results, dailyDir, timestamp)
  
  // Print summary
  console.log('📋 Backup Summary:')
  console.log(`   📦 Total documents: ${manifest.summary.totalDocuments}`)
  console.log(`   ✅ Successful types: ${manifest.summary.successfulTypes}`)
  console.log(`   ❌ Failed types: ${manifest.summary.failedTypes}`)
  console.log(`   ⚠️  Empty types: ${manifest.summary.emptyTypes}`)
  console.log(`   📁 Location: ${dailyDir}`)
  
  if (manifest.summary.failedTypes > 0) {
    console.log('\n❌ Some backups failed:')
    results.filter(r => r.status === 'error').forEach(r => {
      console.log(`   - ${r.contentType}: ${r.error}`)
    })
  }
  
  console.log('\n🎉 Backup completed!')
  
  return manifest
}

// Run backup if called directly
if (require.main === module) {
  runBackup().catch(error => {
    console.error('💥 Backup failed:', error)
    process.exit(1)
  })
}

module.exports = { runBackup }
