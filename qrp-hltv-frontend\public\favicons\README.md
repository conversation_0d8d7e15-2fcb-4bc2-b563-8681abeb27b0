# Favicon Files

This directory contains all favicon and app icon files for QRP HLTV.

## Implemented Files

### Core Files
- `favicon.ico` (48x48, 32x32 multi-size ICO file) → Located in `/public/`
- `favicon.svg` (SVG version for modern browsers)
- `favicon-16x16.png`
- `favicon-32x32.png`
- `favicon-96x96.png`

### Apple Touch Icons
- `apple-touch-icon-180x180.png`

### Android Chrome Icons
- `android-chrome-96x96.png`
- `android-chrome-192x192.png`
- `android-chrome-512x512.png`

### Standard Favicon Sizes
- `favicon-48x48.png`
- `favicon-64x64.png`
- `favicon-96x96.png`
- `favicon-128x128.png`
- `favicon-256x256.png`

### Apple Touch Icons
- `apple-touch-icon-57x57.png`
- `apple-touch-icon-60x60.png`
- `apple-touch-icon-72x72.png`
- `apple-touch-icon-76x76.png`
- `apple-touch-icon-114x114.png`
- `apple-touch-icon-120x120.png`
- `apple-touch-icon-144x144.png`
- `apple-touch-icon-152x152.png`

### Android Chrome Icons
- `android-chrome-36x36.png`
- `android-chrome-48x48.png`
- `android-chrome-72x72.png`
- `android-chrome-96x96.png`
- `android-chrome-144x144.png`
- `android-chrome-256x256.png`
- `android-chrome-384x384.png`

### Microsoft Tiles
- `mstile-70x70.png`
- `mstile-144x144.png`
- `mstile-150x150.png`
- `mstile-310x150.png`
- `mstile-310x310.png`

### Social Media Images
- `og-image.png` (1200x630 for Open Graph)
- `twitter-image.png` (1200x600 for Twitter Cards)

## Design Guidelines

- Use QRP logo/branding
- Background color: #18181b (zinc-950)
- Ensure good contrast and visibility at small sizes
- Keep design simple and recognizable

## Generation Tools

Recommended online tools:
- favicon.io
- realfavicongenerator.net
- favicon-generator.org

## File Naming Convention

All files follow the pattern: `[type]-[size].png` or `[type]-[width]x[height].png`
